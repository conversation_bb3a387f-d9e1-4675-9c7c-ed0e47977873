# Tech Stack

## Document Processing
- **Markdown** - Contract templates & documentation
- **DOCX** - Microsoft Word document generation
- **PDF** - Final contract output format

## Content Management
- **JSON** - Configuration & data structures
- **PNG/SVG** - Logo & image assets

## Development Environment
- **Windows PowerShell** - Primary shell environment
- **Sublime Text** - Code editor (project files present)

## Project Structure
- **Template-driven** - Markdown-based contract templates
- **Multi-version** - Iterative development approach
- **Asset integration** - Logo & branding support
