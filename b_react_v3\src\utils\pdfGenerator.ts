import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import React from 'react';
import ReactDOM from 'react-dom/client';
import ContractTemplate, { ContractData } from '../components/ContractTemplate';

export interface PDFOptions {
  filename?: string;
  quality?: number;
  format?: 'a4' | 'letter';
  orientation?: 'portrait' | 'landscape';
}

/**
 * Generates a PDF from the contract template with exact web-to-PDF mapping
 * This ensures that what you see in the web preview is exactly what gets in the PDF
 */
export const generateContractPDF = async (
  contractData: ContractData,
  options: PDFOptions = {}
): Promise<Blob> => {
  const {
    filename = 'arbeidskontrakt.pdf',
    quality = 1.0,
    format = 'a4',
    orientation = 'portrait'
  } = options;

  // Create a temporary container for PDF rendering
  const tempContainer = document.createElement('div');
  tempContainer.style.position = 'absolute';
  tempContainer.style.left = '-9999px';
  tempContainer.style.top = '0';
  tempContainer.style.width = '210mm'; // A4 width
  tempContainer.style.background = 'white';
  
  // Use the imported contract template to ensure consistency

  // Create the contract element with PDF-specific styling
  const contractElement = React.createElement(ContractTemplate, {
    data: contractData,
    className: 'pdf-mode'
  });

  document.body.appendChild(tempContainer);
  
  try {
    // Render the contract template
    const root = ReactDOM.createRoot(tempContainer);
    root.render(contractElement);

    // Wait for rendering to complete
    await new Promise(resolve => setTimeout(resolve, 100));

    const contractDiv = tempContainer.querySelector('.contract-document') as HTMLElement;
    
    if (!contractDiv) {
      throw new Error('Contract template not found');
    }

    // Generate canvas from the contract element
    const canvas = await html2canvas(contractDiv, {
      scale: 2, // High resolution
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      width: contractDiv.scrollWidth,
      height: contractDiv.scrollHeight,
      logging: false
    });

    // Create PDF
    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF({
      orientation,
      unit: 'mm',
      format
    });

    // Calculate dimensions to fit A4
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    
    // Calculate scaling to fit page while maintaining aspect ratio
    const ratio = Math.min(pdfWidth / (imgWidth * 0.264583), pdfHeight / (imgHeight * 0.264583));
    const scaledWidth = imgWidth * 0.264583 * ratio;
    const scaledHeight = imgHeight * 0.264583 * ratio;
    
    // Center the image on the page
    const x = (pdfWidth - scaledWidth) / 2;
    const y = (pdfHeight - scaledHeight) / 2;

    pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);

    // Clean up
    root.unmount();
    document.body.removeChild(tempContainer);

    return pdf.output('blob');
  } catch (error) {
    // Clean up on error
    if (tempContainer.parentNode) {
      document.body.removeChild(tempContainer);
    }
    throw error;
  }
};

/**
 * Downloads the generated PDF
 */
export const downloadContractPDF = async (
  contractData: ContractData,
  options: PDFOptions = {}
): Promise<void> => {
  const blob = await generateContractPDF(contractData, options);
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = options.filename || 'arbeidskontrakt.pdf';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

/**
 * Validates contract data before PDF generation
 */
export const validateContractData = (data: ContractData): string[] => {
  const errors: string[] = [];
  
  if (!data.employeeName.trim()) {
    errors.push('Navn på arbeidstaker er påkrevd');
  }
  
  if (!data.startDate) {
    errors.push('Startdato er påkrevd');
  }
  
  if (!data.jobTitle.trim()) {
    errors.push('Stillingstittel er påkrevd');
  }
  
  if (data.employmentType === 'temporary' && !data.temporaryUntil) {
    errors.push('Sluttdato for midlertidig ansettelse er påkrevd');
  }
  
  if (data.hasProbation && !data.probationMonths) {
    errors.push('Antall måneder for prøvetid er påkrevd');
  }
  
  return errors;
};

/**
 * Preview contract data formatting (for debugging)
 */
export const previewContractData = (data: ContractData): Record<string, string> => {
  const formatDate = (dateString: string) => {
    if (!dateString) return '__.__.__';
    const date = new Date(dateString);
    return date.toLocaleDateString('no-NO');
  };

  return {
    employeeName: data.employeeName || '_'.repeat(28),
    employeeAddress: data.employeeAddress || '_'.repeat(25),
    employeeBirthDate: data.employeeBirthDate || '_'.repeat(21),
    startDate: formatDate(data.startDate),
    employmentType: data.employmentType === 'permanent' ? 'Fast' : 'Midlertidig',
    temporaryUntil: data.employmentType === 'temporary' ? formatDate(data.temporaryUntil) : '',
    temporaryReason: data.temporaryReason || '_'.repeat(33),
    probationInfo: data.hasProbation ? `${data.probationMonths || '___'} måneder` : 'Ingen',
    jobTitle: data.jobTitle || '_'.repeat(31),
    accountNumber: data.accountNumber || '_'.repeat(10)
  };
};
