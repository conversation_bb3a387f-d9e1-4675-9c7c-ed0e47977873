#!/usr/bin/env python3
"""
Test script for the Arbeidskontrakt Generator
Tests both interactive and non-interactive modes
"""

import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

try:
    from main import Arbeidskontrakt
    print("✅ Successfully imported Arbeidskontrakt class")
except ImportError as e:
    print(f"❌ Failed to import: {e}")
    sys.exit(1)

def test_contract_generation():
    """Test contract generation with sample data"""
    print("\n=== Testing Contract Generation ===")
    
    # Sample employee data
    test_data = {
        'navn': 'Test Testesen',
        'adresse': 'Testveien 456, 1234 Testby',
        'fodselsdato': '15.06.1985',
        'startdato': '01.06.2025',
        'stilling': 'Anleggsgartner',
        'timelonn': '320',
        'sluttdato': '31.10.2025',
        'midlertidig_grunn': 'Sesongarbeid',
        'provetid': '2'
    }
    
    generator = Arbeidskontrakt()
    
    try:
        # Test contract generation
        output_file = "test_contract.docx"
        generator.create_contract(test_data, output_file)
        
        # Check if file was created
        if os.path.exists(output_file):
            print(f"✅ Contract successfully generated: {output_file}")
            file_size = os.path.getsize(output_file)
            print(f"   File size: {file_size} bytes")
            return True
        else:
            print(f"❌ Contract file not found: {output_file}")
            return False
            
    except Exception as e:
        print(f"❌ Error generating contract: {e}")
        return False

def test_company_info():
    """Test company information is correct"""
    print("\n=== Testing Company Information ===")
    
    generator = Arbeidskontrakt()
    company = generator.company_info
    
    expected = {
        "name": "Ringerike Landskap AS",
        "org_nr": "***********",
        "address": "Birchs vei 7, 3530 Røyse"
    }
    
    for key, expected_value in expected.items():
        if company.get(key) == expected_value:
            print(f"✅ {key}: {company[key]}")
        else:
            print(f"❌ {key}: Expected '{expected_value}', got '{company.get(key)}'")
            return False
    
    return True

def main():
    """Run all tests"""
    print("🧪 Arbeidskontrakt Generator Test Suite")
    print("=" * 50)
    
    tests = [
        test_company_info,
        test_contract_generation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test failed: {test.__name__}")
        except Exception as e:
            print(f"❌ Test error in {test.__name__}: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The arbeidskontrakt generator is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
