# Technology Stack

## Core Framework
- **React 18** - Component-based UI library
- **TypeScript** - Type-safe JavaScript
- **Vite** - Fast build tool and dev server

## UI & Styling
- **shadcn/ui** - Modern component library
- **Radix UI** - Accessible primitives
- **Tailwind CSS** - Utility-first styling
- **Lucide React** - Icon library

## State & Data
- **TanStack Query** - Server state management
- **React Hook Form** - Form handling
- **Zod** - Schema validation

## Routing & Navigation
- **React Router DOM** - Client-side routing

## PDF Generation
- **jsPDF** - PDF document creation
- **html2canvas** - HTML to canvas conversion

## Development Tools
- **ESLint** - Code linting
- **PostCSS** - CSS processing
- **Lovable** - AI-powered development platform

## Package Management
- **npm/bun** - Dependency management
