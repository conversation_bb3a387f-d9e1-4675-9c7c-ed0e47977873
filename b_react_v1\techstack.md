# Tech Stack

## Core
- **React 18** + **TypeScript** + **Vite**
- **SWC** (Fast Rust-based compiler)

## UI Framework
- **shadcn/ui** (Radix UI primitives)
- **Tailwind CSS** + **CSS Variables**
- **Lucide React** (Icons)

## State & Forms
- **TanStack Query** (Server state)
- **React Hook Form** + **Zod** (Validation)

## Routing & Navigation
- **React Router DOM**

## PDF Generation
- **jsPDF** + **html2canvas**

## Development
- **ESLint** + **TypeScript ESLint**
- **PostCSS** + **Autoprefixer**
- **Bun** (Package manager)
