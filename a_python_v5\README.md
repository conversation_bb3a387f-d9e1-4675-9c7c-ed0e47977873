# Arbeidskontrakt Generator for Ringerike Landskap AS

A simple Python tool to generate employment contracts (arbeidskontrakt) as DOCX files for Ringerike Landskap AS.

## Features

- ✅ Generates professional employment contracts in Norwegian
- ✅ Complies with Norwegian labor law requirements (AML § 14-6)
- ✅ Fits on one A4 page
- ✅ Supports both permanent and temporary employment
- ✅ Interactive mode for easy data entry
- ✅ Outputs to Microsoft Word (.docx) format

## Quick Start

### 1. Setup Environment

```powershell
# Initialize Python virtual environment and install dependencies
.\py_venv_init.bat
```

### 2. Run the Generator

```powershell
# Interactive mode (recommended)
.\src\main.bat

# Or run directly with Python
python src\main.py --prompt
```

### 3. Fill in Employee Information

The program will prompt you for:
- Employee name (required)
- Address
- Birth date
- Start date
- Position title
- Hourly wage (default: 300 NOK)
- Employment type (permanent/temporary)
- Probation period

### 4. Get Your Contract

The generated DOCX file will be saved as `arbeidskontrakt.docx` in the current directory.

## Usage Examples

### Interactive Mode
```powershell
python src\main.py --prompt
```

### Generate with Example Data
```powershell
python src\main.py
```

### Custom Output File
```powershell
python src\main.py --prompt --output "kontrakt_ola_nordmann.docx"
```

## Contract Template

The generated contract includes all required sections per Norwegian law:

1. **Ansettelsesforhold** - Employment relationship details
2. **Arbeidssted** - Workplace information  
3. **Stilling og oppgaver** - Position and job tasks
4. **Arbeidstid** - Working hours (37.5h/week, 07:00-15:00)
5. **Lønn og godtgjørelse** - Salary and compensation
6. **Ferie og feriepenger** - Vacation and vacation pay (5 weeks, 12%)
7. **Oppsigelse** - Termination notice periods
8. **Diverse** - Miscellaneous terms

## Company Information

Pre-configured for:
- **Company**: Ringerike Landskap AS
- **Org. Nr**: ***********  
- **Address**: Birchs vei 7, 3530 Røyse
- **Business**: Landscaping and construction services

## Requirements

- Python 3.7+
- python-docx library (automatically installed via requirements.txt)

## File Structure

```
py_arbeidskontrakt_v5/
├── src/
│   ├── main.py          # Main application
│   └── main.bat         # Windows launcher script
├── py_venv_init.bat     # Environment setup script
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

## Troubleshooting

### "python-docx library not installed"
Run the environment setup script:
```powershell
.\py_venv_init.bat
```

### Permission errors when saving DOCX
Make sure the output directory is writable and no other program has the file open.

### Virtual environment issues
Delete the `venv` folder and run `py_venv_init.bat` again.

## Legal Compliance

This generator creates contracts that meet the minimum requirements of:
- **Arbeidsmiljøloven § 14-6** (Norwegian Working Environment Act)
- **Ferieloven** (Norwegian Holiday Act)
- Standard Norwegian employment law practices

⚠️ **Disclaimer**: This tool generates standard employment contracts. For complex employment situations or legal advice, consult with a qualified Norwegian employment lawyer.
