# Tech Stack

## Core
- **React 18** + **TypeScript** + **Vite**
- **SWC** (Fast Rust-based compiler)

## UI & Styling
- **shadcn/ui** (Radix UI primitives)
- **Tailwind CSS** + **CSS Variables**
- **Lucide React** (Icons)

## State & Forms
- **React Hook Form** + **Zod** (Validation)
- **TanStack Query** (Server state)

## Routing & Navigation
- **React Router DOM**

## Development
- **ESLint** + **TypeScript ESLint**
- **PostCSS** + **Autoprefixer**
- **Lovable Tagger** (Dev tooling)

## Package Management
- **npm** (Primary) + **Bun** (Alternative)
