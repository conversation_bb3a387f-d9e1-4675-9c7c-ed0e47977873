# Technology Stack

## Core
- **Python 3.8+** — Primary language
- **ReportLab 4.0+** — PDF generation engine
- **setuptools** — Package management

## PDF Processing
- **PyPDF2** — Form field extraction
- **pdfplumber** — Text & layout analysis  
- **PyMuPDF (fitz)** — Comprehensive PDF manipulation
- **pdfminer.six** — Deep structure analysis
- **Pillow** — Image processing

## Development
- **pytest** — Testing framework
- **black** — Code formatting
- **flake8** — Linting
- **mypy** — Type checking

## Environment
- **Windows PowerShell** — Automation scripts
- **Virtual Environment** — Dependency isolation
- **<PERSON>ch Scripts** — Legacy environment setup

## Architecture
Modular PDF engine with clean abstraction layers for document generation, interactive forms, and Norwegian employment contract specialization.
