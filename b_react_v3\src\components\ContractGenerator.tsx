import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Printer, Download, Eye } from "lucide-react";
import { toast } from "@/components/ui/sonner";
import ContractTemplate, { ContractData } from "./ContractTemplate";
import { downloadContractPDF, validateContractData } from "../utils/pdfGenerator";
import "../styles/contract.css";

const ContractGenerator = () => {
  const [contractData, setContractData] = useState<ContractData>({
    employeeName: '',
    employeeAddress: '',
    employeeBirthDate: '',
    startDate: '',
    employmentType: 'permanent',
    temporaryUntil: '',
    temporaryReason: '',
    hasProbation: false,
    probationMonths: '',
    jobTitle: '',
    accountNumber: ''
  });

  const [showContract, setShowContract] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [showLivePreview, setShowLivePreview] = useState(false);

  const handleInputChange = (field: keyof ContractData, value: string | boolean) => {
    setContractData(prev => ({ ...prev, [field]: value }));
  };

  const generateContract = () => {
    const errors = validateContractData(contractData);
    if (errors.length > 0) {
      toast.error("Vennligst fyll ut alle påkrevde felt", {
        description: errors.join(', ')
      });
      return;
    }
    setShowContract(true);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = async () => {
    const errors = validateContractData(contractData);
    if (errors.length > 0) {
      toast.error("Kan ikke generere PDF", {
        description: errors.join(', ')
      });
      return;
    }

    setIsGeneratingPDF(true);
    try {
      await downloadContractPDF(contractData, {
        filename: `arbeidskontrakt_${contractData.employeeName.replace(/\s+/g, '_')}.pdf`
      });
      toast.success("PDF lastet ned!", {
        description: "Kontrakten er klar for utskrift"
      });
    } catch (error) {
      console.error('PDF generation error:', error);
      toast.error("Feil ved PDF-generering", {
        description: "Prøv igjen eller bruk utskrift-funksjonen"
      });
    } finally {
      setIsGeneratingPDF(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 p-4">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-center text-green-800">
              Arbeidskontrakt Generator
            </CardTitle>
            <p className="text-center text-gray-600">Ringerike Landskap AS</p>
          </CardHeader>
          <CardContent>
            {!showContract ? (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="employeeName">Navn på arbeidstaker *</Label>
                    <Input
                      id="employeeName"
                      value={contractData.employeeName}
                      onChange={(e) => handleInputChange('employeeName', e.target.value)}
                      placeholder="Fullt navn"
                    />
                  </div>
                  <div>
                    <Label htmlFor="employeeBirthDate">Fødselsdato *</Label>
                    <Input
                      id="employeeBirthDate"
                      value={contractData.employeeBirthDate}
                      onChange={(e) => handleInputChange('employeeBirthDate', e.target.value)}
                      placeholder="dd.mm.åååå"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="employeeAddress">Adresse *</Label>
                  <Input
                    id="employeeAddress"
                    value={contractData.employeeAddress}
                    onChange={(e) => handleInputChange('employeeAddress', e.target.value)}
                    placeholder="Gate/vei, postnummer og sted"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate">Startdato *</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={contractData.startDate}
                      onChange={(e) => handleInputChange('startDate', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="jobTitle">Stillingstittel *</Label>
                    <Input
                      id="jobTitle"
                      value={contractData.jobTitle}
                      onChange={(e) => handleInputChange('jobTitle', e.target.value)}
                      placeholder="F.eks. Anleggsgartner, Grunnarbeider"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="accountNumber">Kontonummer</Label>
                  <Input
                    id="accountNumber"
                    value={contractData.accountNumber}
                    onChange={(e) => handleInputChange('accountNumber', e.target.value)}
                    placeholder="XXXX.XX.XXXXX"
                  />
                </div>

                <Separator />

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="temporary"
                      checked={contractData.employmentType === 'temporary'}
                      onCheckedChange={(checked) => 
                        handleInputChange('employmentType', checked ? 'temporary' : 'permanent')
                      }
                    />
                    <Label htmlFor="temporary">Midlertidig ansettelse</Label>
                  </div>

                  {contractData.employmentType === 'temporary' && (
                    <div className="ml-6 space-y-3">
                      <div>
                        <Label htmlFor="temporaryUntil">Midlertidig til dato</Label>
                        <Input
                          id="temporaryUntil"
                          type="date"
                          value={contractData.temporaryUntil}
                          onChange={(e) => handleInputChange('temporaryUntil', e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="temporaryReason">Grunnlag for midlertidig ansettelse</Label>
                        <Textarea
                          id="temporaryReason"
                          value={contractData.temporaryReason}
                          onChange={(e) => handleInputChange('temporaryReason', e.target.value)}
                          placeholder="F.eks. Vikariat, sesongarbeid, prosjekt"
                          rows={2}
                        />
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="probation"
                      checked={contractData.hasProbation}
                      onCheckedChange={(checked) => handleInputChange('hasProbation', checked as boolean)}
                    />
                    <Label htmlFor="probation">Prøvetid</Label>
                  </div>

                  {contractData.hasProbation && (
                    <div className="ml-6">
                      <Label htmlFor="probationMonths">Antall måneder (maks 6)</Label>
                      <Input
                        id="probationMonths"
                        type="number"
                        min="1"
                        max="6"
                        value={contractData.probationMonths}
                        onChange={(e) => handleInputChange('probationMonths', e.target.value)}
                        placeholder="1-6"
                      />
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  <Button onClick={generateContract} className="flex-1 bg-green-600 hover:bg-green-700">
                    Generer arbeidskontrakt
                  </Button>
                  <Button
                    onClick={() => setShowLivePreview(!showLivePreview)}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <Eye className="w-4 h-4" />
                    {showLivePreview ? 'Skjul' : 'Vis'} forhåndsvisning
                  </Button>
                </div>

                {showLivePreview && (
                  <div className="mt-6 border-t pt-6">
                    <h3 className="text-lg font-semibold mb-4">Live forhåndsvisning</h3>
                    <div
                      className="border rounded-lg bg-white"
                      style={{
                        maxHeight: '60vh',
                        overflow: 'auto'
                      }}
                    >
                      <ContractTemplate data={contractData} className="preview-mode" />
                    </div>
                    <p className="text-sm text-gray-600 mt-2">
                      Forhåndsvisningen oppdateres automatisk når du endrer feltene over.
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center no-print">
                  <h3 className="text-lg font-semibold">Generert arbeidskontrakt</h3>
                  <div className="flex gap-2">
                    <Button
                      onClick={handleDownloadPDF}
                      disabled={isGeneratingPDF}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      {isGeneratingPDF ? 'Genererer PDF...' : 'Last ned PDF'}
                    </Button>
                    <Button onClick={handlePrint} className="bg-blue-600 hover:bg-blue-700">
                      <Printer className="w-4 h-4 mr-2" />
                      Skriv ut
                    </Button>
                    <Button onClick={() => setShowContract(false)} variant="outline">
                      Tilbake til skjema
                    </Button>
                  </div>
                </div>

                <div
                  className="contract-display border rounded-lg bg-white"
                  style={{
                    maxHeight: '70vh',
                    overflow: 'auto'
                  }}
                >
                  <ContractTemplate data={contractData} className="preview-mode" />
                </div>

                <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded no-print">
                  <strong>Automatisk synkronisering:</strong> Denne kontrakten er automatisk synkronisert mellom
                  web-visning og PDF-utskrift. Alle endringer i skjemaet oppdateres umiddelbart i både forhåndsvisning
                  og PDF-generering. Kontrakten oppfyller alle krav i arbeidsmiljøloven § 14-6.
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ContractGenerator;
