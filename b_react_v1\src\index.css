@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Contract Document Styles - Unified for Preview and PDF */
.contract-document-preview, .contract-document-pdf {
  font-family: 'Times New Roman', serif;
  line-height: 1.4;
  color: #000;
  max-width: 210mm;
  margin: 0 auto;
  background: white;
  font-size: 11pt;
}

.contract-document-pdf {
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
  border: none !important;
  border-radius: 0 !important;
  box-sizing: border-box !important;
}

.contract-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  gap: 20px;
}

.company-logo {
  height: 40px;
  width: auto;
  max-width: 120px;
}

.contract-title {
  text-align: center;
  font-size: 16pt;
  font-weight: bold;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  flex-shrink: 0;
}

.parties-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.party-block {
  flex: 1;
  margin-right: 20px;
}

.party-block:last-child {
  margin-right: 0;
}

.section-divider {
  border: none;
  border-top: 1px solid #000;
  margin: 12px 0;
}

.contract-section {
  margin-bottom: 12px;
}

.contract-section h2 {
  font-size: 12pt;
  font-weight: bold;
  margin-bottom: 6px;
  color: #000;
}

.contract-list {
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
}

.contract-list li {
  margin-bottom: 2px;
}

.compensation-table, .termination-table, .signature-table {
  width: 100%;
  border-collapse: collapse;
  margin: 8px 0;
}

.compensation-table td, .termination-table td {
  border: 1px solid #000;
  padding: 4px 8px;
  font-size: 10pt;
}

.signature-table td {
  padding: 8px;
  vertical-align: bottom;
}

.legal-note {
  font-size: 10pt;
  margin-top: 4px;
}

.signature-section {
  margin-top: 20px;
}

.footer-note {
  text-align: center;
  font-size: 9pt;
  margin-top: 15px;
}

@media print {
  .contract-document-preview, .contract-document-pdf {
    padding: 15mm;
    font-size: 10pt;
  }

  .contract-header {
    gap: 15px;
  }

  .company-logo {
    height: 35px;
    max-width: 100px;
  }

  .contract-title {
    font-size: 14pt;
  }

  .contract-section h2 {
    font-size: 11pt;
  }
}