# Contract Generator for Ringerike Landskap AS

## Core Purpose
This is a specialized **Norwegian employment contract generator** designed specifically for Ringerike Landskap AS, a landscaping and groundwork company. The application creates legally compliant employment contracts (arbeidsavtaler) that meet Norwegian labor law requirements.

## Unique Workflow

### 1. Data Collection Phase
- User fills out a comprehensive form with employee details
- Collects both mandatory and optional information
- Provides conditional fields based on employment type

### 2. Contract Generation Phase
- Transforms collected data into a professionally formatted contract
- Applies Norwegian legal standards and company-specific terms
- Generates a print-ready document

### 3. Review & Output Phase
- Displays the generated contract for review
- Allows users to return to edit the form
- **PDF Download**: Generate and download professional PDF contracts
- Provides legal compliance confirmation

## Core Functionality

### Data Fields Collected
- **Employee Information**: Name, address, birth date
- **Employment Details**: Start date, job title, account number
- **Employment Type**: Permanent vs. temporary (with end date and justification)
- **Probation Period**: Optional with duration (max 6 months)

### Contract Template Features
- **Company Information**: Pre-filled Ringerike Landskap AS details (org. nr, address)
- **Standardized Terms**: 
  - 37.5 hours/week, 07:00-15:00 schedule
  - 300 NOK hourly rate
  - 40%+ overtime compensation
  - 3.50 NOK/km mileage reimbursement
  - 2% minimum pension contribution
- **Legal Compliance**: Follows Norwegian Labor Law (AML § 14-6)
- **Professional Formatting**: A4-ready with Times New Roman styling

### Business Logic
- **Conditional Content**: Different sections appear based on employment type
- **Date Formatting**: Norwegian format (dd.mm.yyyy)
- **Validation**: Probation period limited to 6 months maximum
- **Fallback Values**: Underscores for missing required fields
- **PDF Generation**: High-quality PDF export with automatic filename generation

### PDF Download Features
- **Professional Quality**: High-resolution PDF generation optimized for A4 printing
- **Smart Filename**: Automatically generates filename with employee name and date
- **Loading States**: Visual feedback during PDF generation process
- **Error Handling**: Graceful error handling with user feedback
- **A4 Optimization**: Content automatically scaled to fit A4 page dimensions

## Technical Architecture
- **React/TypeScript** single-page application
- **shadcn/ui** component library for modern UI
- **Tailwind CSS** for styling
- **Vite** for development and building
- **Client-side only** - no backend dependencies

## Unique Value Proposition
This utility streamlines the contract creation process for a specific Norwegian landscaping company, ensuring legal compliance while maintaining professional presentation standards. It eliminates manual contract drafting and reduces legal risks through standardized, compliant templates.

## Technologies Used
- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- html2canvas (PDF generation)
- jsPDF (PDF creation)

## Development Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## Project Structure
```
src/
├── components/
│   ├── ui/           # shadcn/ui components
│   └── ContractGenerator.tsx  # Main application component
├── pages/
│   ├── Index.tsx     # Home page
│   └── NotFound.tsx  # 404 page
├── lib/
│   └── utils.ts      # Utility functions
└── hooks/
    └── use-mobile.tsx # Mobile detection hook
```
